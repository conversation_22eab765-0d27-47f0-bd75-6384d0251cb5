// Mock user data
const MOCK_USER = {
  username: 'admin',
  password: 'admin123',
  name: 'مدير النظام',
  role: 'admin'
};


// Mock token generation
function generateToken(user) {
  // This is a simple mock token. In production, use proper JWT
  const payload = {
    sub: user.username,
    name: user.name,
    role: user.role,
    exp: new Date().getTime() + (24 * 60 * 60 * 1000) // 24 hours
  };

  // Convert to base64 with UTF-8 support
  const encoded = btoa(unescape(encodeURIComponent(JSON.stringify(payload))))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  return encoded;
}

export function mockLogin(credentials) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (credentials.username === MOCK_USER.username && 
          credentials.password === MOCK_USER.password) {
        resolve({
          token: generateToken(MOCK_USER),
          user: {
            username: MOCK_USER.username,
            name: MOCK_USER.name,
            role: MOCK_USER.role
          }
        });
      } else {
        reject(new Error('اسم المستخدم أو كلمة المرور غير صحيحة'));
      }
    }, 500); // Simulate network delay
  });
}