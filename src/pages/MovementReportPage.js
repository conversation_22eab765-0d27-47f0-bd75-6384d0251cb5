// بنجيب الحاجات اللي محتاجينها من React والمكتبات التانية
import { useState } from 'react'; // ده Hook عشان نتحكم في البيانات اللي بتتغير في الصفحة
import { Search, FileText, Download, Printer, ArrowDown, ArrowUp } from 'lucide-react'; // دي أيقونات حلوة من مكتبة Lucide
import DashboardLayout from '../components/layouts/DashboardLayout'; // ده التصميم الأساسي للوحة التحكم

// ده الكومبوننت بتاع صفحة تقرير حركة المخزون - يعني الصفحة اللي بتعرض كل الحركات اللي حصلت في المخزن
export default function MovementReportPage() {
  // دي الفلاتر اللي المستخدم هيختار منها عشان يفلتر التقرير زي ما هو عايز
  const [filters, setFilters] = useState({
    warehouse: '', // أي مخزن عايز يشوف حركته
    material: '', // أي مادة معينة عايز يشوف حركتها
    movementType: '', // نوع الحركة - داخل ولا خارج ولا إيه
    startDate: '', // من إمتى عايز يشوف الحركات
    endDate: '', // لحد إمتى عايز يشوف الحركات
  });

  // دي البيانات الوهمية بتاعة الحركات - عشان نجرب بيها الصفحة
  const [movements] = useState([
    {
      id: 1, // رقم الحركة
      date: '2025-05-01', // إمتى حصلت الحركة دي
      type: 'إدخال', // نوع الحركة - داخلة ولا خارجة
      material: 'حديد تسليح', // إيه اسم المادة اللي اتحركت
      materialCode: 'M001', // كود المادة عشان نعرفها بسهولة
      warehouse: 'المخزن الرئيسي', // أي مخزن حصلت فيه الحركة
      quantity: 100, // كام الكمية اللي اتحركت
      unit: 'طن', // وحدة القياس بتاعة المادة
      reference: 'PO-001', // رقم الفاتورة أو الإذن
      user: 'أحمد محمد', // مين اللي عمل الحركة دي
      notes: 'استلام طلبية من المورد' // أي ملاحظات إضافية
    },
    {
      id: 2,
      date: '2025-05-02',
      type: 'إخراج',
      material: 'حديد تسليح',
      materialCode: 'M001',
      warehouse: 'المخزن الرئيسي',
      quantity: 20,
      unit: 'طن',
      reference: 'SO-001',
      user: 'محمد علي',
      notes: 'صرف لمشروع البناء'
    },
  ]);

  // دي القوائم اللي هنحط فيها الاختيارات بتاعة الفلاتر
  const warehouses = ['المخزن الرئيسي', 'مخزن المواد الخام']; // كل المخازن اللي عندنا
  const materials = ['حديد تسليح', 'اسمنت', 'رمل']; // كل المواد اللي عندنا
  const movementTypes = ['إدخال', 'إخراج', 'تحويل', 'جرد']; // كل أنواع الحركات اللي ممكن تحصل

  // دي الفانكشن اللي بتصدر التقرير - لسه مش مخلصة
  const handleExport = (format) => {
    console.log(`Exporting report as ${format}`); // TODO: لازم نخلص الجزء ده
  };

  // دي الفانكشن اللي بتطبع التقرير - برضو مش مخلصة
  const handlePrint = () => {
    console.log('Printing report'); // TODO: ودي كمان لازم نخلصها
  };

  // دي فانكشن بتجيب الأيقونة المناسبة لكل نوع حركة
  const getMovementIcon = (type) => {
    switch (type) {
      case 'إدخال':
        return <ArrowDown size={20} className="text-green-500" />; // سهم لتحت أخضر للإدخال
      case 'إخراج':
        return <ArrowUp size={20} className="text-red-500" />; // سهم لفوق أحمر للإخراج
      default:
        return null; // مفيش أيقونة لو النوع مش معروف
    }
  };

  // ده المحتوى الأساسي بتاع الصفحة
  const content = (
    <div className="p-6"> {/* حاوي عام للصفحة مع مسافات */}
      {/* الهيدر بتاع الصفحة مع الأزرار */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">تقرير حركة المخزون</h1>
        <div className="flex gap-2"> {/* مجموعة أزرار التصدير والطباعة */}
          {/* زر تصدير Excel */}
          <button
            onClick={() => handleExport('excel')} // لما يدوس هيصدر Excel
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            <Download size={20} /> {/* أيقونة التحميل */}
            تصدير Excel
          </button>
          {/* زر تصدير PDF */}
          <button
            onClick={() => handleExport('pdf')} // لما يدوس هيصدر PDF
            className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            <FileText size={20} /> {/* أيقونة الملف */}
            تصدير PDF
          </button>
          {/* زر الطباعة */}
          <button
            onClick={handlePrint} // لما يدوس هيطبع التقرير
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Printer size={20} /> {/* أيقونة الطابعة */}
            طباعة
          </button>
        </div>
      </div>

      {/* قسم الفلاتر - عشان المستخدم يقدر يفلتر التقرير زي ما هو عايز */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4"> {/* شبكة متجاوبة للفلاتر */}
          {/* فلتر المخزن */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              المخزن
            </label>
            <select
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.warehouse} // القيمة الحالية للفلتر
              onChange={(e) => setFilters({ ...filters, warehouse: e.target.value })} // لما يغير الاختيار
              dir="rtl" // اتجاه النص من اليمين لليسار
            >
              <option value="">الكل</option> {/* اختيار "الكل" يعني مش هيفلتر */}
              {warehouses.map(warehouse => ( // بنعرض كل المخازن اللي عندنا
                <option key={warehouse} value={warehouse}>{warehouse}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              المادة
            </label>
            <select
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.material}
              onChange={(e) => setFilters({ ...filters, material: e.target.value })}
              dir="rtl"
            >
              <option value="">الكل</option>
              {materials.map(material => (
                <option key={material} value={material}>{material}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              نوع الحركة
            </label>
            <select
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.movementType}
              onChange={(e) => setFilters({ ...filters, movementType: e.target.value })}
              dir="rtl"
            >
              <option value="">الكل</option>
              {movementTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              من تاريخ
            </label>
            <input
              type="date"
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.startDate}
              onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              إلى تاريخ
            </label>
            <input
              type="date"
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.endDate}
              onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
            />
          </div>
        </div>
      </div>

      {/* جدول الحركات - ده اللي هيعرض كل الحركات اللي حصلت */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto"> {/* عشان الجدول يتحرك أفقياً على الموبايل */}
          <table className="min-w-full divide-y divide-gray-200">
            {/* رأس الجدول */}
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">نوع الحركة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">كود المادة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم المادة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المخزن</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المرجع</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المستخدم</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">ملاحظات</th>
              </tr>
            </thead>
            {/* محتوى الجدول */}
            <tbody className="bg-white divide-y divide-gray-200">
              {/* بنعرض كل حركة في صف لوحدها */}
              {movements.map((movement) => (
                <tr key={movement.id} className="hover:bg-gray-50"> {/* لما يحوم عليها بالماوس تتلون */}
                  <td className="px-6 py-4 whitespace-nowrap text-right">{new Date(movement.date).toLocaleDateString('ar-EG')}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2 justify-end"> {/* نوع الحركة مع الأيقونة */}
                      <span className={`${
                        movement.type === 'إدخال' ? 'text-green-700' : 'text-red-700' // لون أخضر للإدخال وأحمر للإخراج
                      }`}>
                        {movement.type}
                      </span>
                      {getMovementIcon(movement.type)} {/* الأيقونة المناسبة */}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{movement.materialCode}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{movement.material}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{movement.warehouse}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right font-medium">{movement.quantity}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{movement.unit}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{movement.reference}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{movement.user}</td>
                  <td className="px-6 py-4 text-right">{movement.notes}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* ملخص الإحصائيات - عشان نعرف كام حركة إجمالي وكام إدخال وكام إخراج */}
        <div className="bg-gray-50 px-6 py-4">
          <div className="flex justify-between text-sm text-gray-700">
            <div>إجمالي الحركات: <span className="font-semibold">{movements.length}</span></div>
            <div>
              الإدخالات: <span className="font-semibold text-green-600">{movements.filter(m => m.type === 'إدخال').length}</span> |
              الإخراجات: <span className="font-semibold text-red-600">{movements.filter(m => m.type === 'إخراج').length}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // في الآخر بنرجع كل المحتوى ده جوا التصميم الأساسي بتاع لوحة التحكم
  return <DashboardLayout>{content}</DashboardLayout>;
}